package com.cryptoexchange.service.impl;

import com.cryptoexchange.common.PageResult;
import com.cryptoexchange.common.Result;
import com.cryptoexchange.dto.request.*;
import com.cryptoexchange.dto.response.*;
import com.cryptoexchange.entity.*;
import com.cryptoexchange.mapper.*;
import com.cryptoexchange.service.WalletService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 钱包服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WalletServiceImpl implements WalletService {

    private final UserWalletMapper userWalletMapper;
    private final TransactionRecordMapper transactionRecordMapper;
    private final DepositMapper depositMapper;
    private final WithdrawMapper withdrawMapper;

    // 移除@Override注解，这个方法不在接口中
    public Result<List<WalletResponse>> getUserWallets(Long userId) {
        try {
            List<UserWallet> wallets = userWalletMapper.selectByUserId(userId);
            List<WalletResponse> responses = wallets.stream()
                .map(this::convertToWalletResponse)
                .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            log.error("获取用户钱包列表失败", e);
            return Result.error("获取钱包列表失败");
        }
    }

    @Override
    public Result<List<WalletResponse>> getUserWallets(Long userId, Integer walletType) {
        try {
            List<UserWallet> wallets = userWalletMapper.selectByUserIdAndType(userId, walletType);
            List<WalletResponse> responses = wallets.stream()
                .map(this::convertToWalletResponse)
                .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            log.error("获取用户钱包列表失败", e);
            return Result.error("获取钱包列表失败");
        }
    }

    @Override
    public List<WalletResponse> getUserWallets(Long userId, String currency, Integer walletType) {
        try {
            List<UserWallet> wallets;
            if (currency != null && !currency.isEmpty()) {
                // 如果指定了币种，按币种和钱包类型过滤
                UserWallet wallet = userWalletMapper.selectByUserAndCurrency(userId, currency, walletType.toString());
                wallets = wallet != null ? Arrays.asList(wallet) : new ArrayList<>();
            } else {
                // 如果没有指定币种，按钱包类型过滤
                wallets = userWalletMapper.selectByUserIdAndType(userId, walletType);
            }
            return wallets.stream()
                .map(this::convertToWalletResponse)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户钱包列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public Result<WalletResponse> getUserWallet(Long userId, String currency, Integer walletType) {
        try {
            UserWallet wallet = userWalletMapper.selectByUserAndCurrency(userId, currency, walletType.toString());
            if (wallet == null) {
                return Result.error("钱包不存在");
            }
            WalletResponse response = convertToWalletResponse(wallet);
            return Result.success(response);
        } catch (Exception e) {
            log.error("获取钱包失败", e);
            return Result.error("获取钱包失败");
        }
    }

    @Override
    @Transactional
    public Result<WalletResponse> createUserWallet(Long userId, String currency, Integer walletType) {
        try {
            log.info("创建钱包请求: userId={}, currency={}, walletType={}", userId, currency, walletType);
            
            UserWallet wallet = new UserWallet();
            wallet.setUserId(userId);
            wallet.setCurrency(currency);
            wallet.setWalletType(walletType);
            wallet.setAvailableBalance(BigDecimal.ZERO);
            wallet.setFrozenBalance(BigDecimal.ZERO);
            wallet.setTotalBalance(BigDecimal.ZERO);
            wallet.setAddress(generateWalletAddress(currency));
            wallet.setStatus(1);
            wallet.setCreateTime(LocalDateTime.now());
            wallet.setUpdateTime(LocalDateTime.now());

            userWalletMapper.insert(wallet);
            
            WalletResponse response = convertToWalletResponse(wallet);
            return Result.success(response);
        } catch (Exception e) {
            log.error("创建钱包失败", e);
            return Result.error("创建钱包失败");
        }
    }

    @Override
    @Transactional
    public Result<Void> updateBalance(Long userId, String currency, BigDecimal amount, Integer type) {
        try {
            UserWallet wallet = userWalletMapper.selectByUserAndCurrency(userId, currency, type.toString());
            if (wallet == null) {
                return Result.error("钱包不存在");
            }

            BigDecimal newBalance = wallet.getAvailableBalance().add(amount);
            if (newBalance.compareTo(BigDecimal.ZERO) < 0) {
                return Result.error("余额不足");
            }

            wallet.setAvailableBalance(newBalance);
            wallet.setUpdateTime(LocalDateTime.now());
            userWalletMapper.updateById(wallet);

            // 记录交易
            recordTransaction(userId, currency, amount, type, "BALANCE_UPDATE", "余额更新");

            return Result.success();
        } catch (Exception e) {
            log.error("更新余额失败", e);
            return Result.error("更新余额失败");
        }
    }

    @Override
    @Transactional
    public Result<Void> freezeBalance(Long userId, String currency, BigDecimal amount) {
        try {
            // 默认使用现货钱包
            Integer defaultWalletType = 1;
            UserWallet wallet = userWalletMapper.selectByUserAndCurrency(userId, currency, defaultWalletType.toString());
            if (wallet == null) {
                return Result.error("钱包不存在");
            }

            if (wallet.getAvailableBalance().compareTo(amount) < 0) {
                return Result.error("可用余额不足");
            }

            wallet.setAvailableBalance(wallet.getAvailableBalance().subtract(amount));
            wallet.setFrozenBalance(wallet.getFrozenBalance().add(amount));
            wallet.setUpdateTime(LocalDateTime.now());
            userWalletMapper.updateById(wallet);

            // 记录交易
            recordTransaction(userId, currency, amount.negate(), defaultWalletType, "FREEZE", "冻结余额");

            return Result.success();
        } catch (Exception e) {
            log.error("冻结余额失败", e);
            return Result.error("冻结余额失败");
        }
    }

    @Override
    @Transactional
    public Result<Void> unfreezeBalance(Long userId, String currency, BigDecimal amount) {
        try {
            // 默认使用现货钱包
            Integer defaultWalletType = 1;
            UserWallet wallet = userWalletMapper.selectByUserAndCurrency(userId, currency, defaultWalletType.toString());
            if (wallet == null) {
                return Result.error("钱包不存在");
            }

            if (wallet.getFrozenBalance().compareTo(amount) < 0) {
                return Result.error("冻结余额不足");
            }

            wallet.setAvailableBalance(wallet.getAvailableBalance().add(amount));
            wallet.setFrozenBalance(wallet.getFrozenBalance().subtract(amount));
            wallet.setUpdateTime(LocalDateTime.now());
            userWalletMapper.updateById(wallet);

            // 记录交易
            recordTransaction(userId, currency, amount, defaultWalletType, "UNFREEZE", "解冻余额");

            return Result.success();
        } catch (Exception e) {
            log.error("解冻余额失败", e);
            return Result.error("解冻余额失败");
        }
    }

    @Override
    @Transactional
    public Result<TransactionResponse> deposit(DepositRequest request) {
        try {
            // 参数验证
            if (!validateDepositRequest(request)) {
                return Result.error("充值参数验证失败");
            }
            
            // 创建充值记录
            Deposit deposit = new Deposit();
            // deposit.setUserId(userId); // 需要从上下文获取
            deposit.setCurrency(request.getCurrency());
            deposit.setAmount(request.getAmount());
            deposit.setAddress(request.getAddress());
            deposit.setTxHash(request.getTxHash());
            deposit.setConfirmations(0);
            deposit.setRequiredConfirmations(getRequiredConfirmations(request.getCurrency()));
            deposit.setStatus("PENDING"); // 待确认
            deposit.setCreateTime(LocalDateTime.now());
            deposit.setUpdateTime(LocalDateTime.now());
            
            depositMapper.insert(deposit);
            
            // 如果是内部转账或测试环境，直接确认
            if (request.getIsInternal() != null && request.getIsInternal()) {
                confirmDeposit(deposit.getId());
            }
            
            // 构造返回结果
            TransactionResponse response = new TransactionResponse();
            response.setTransactionId(deposit.getId());
            response.setCurrency(deposit.getCurrency());
            response.setAmount(deposit.getAmount());
            response.setStatus(deposit.getStatus());
            response.setCreateTime(deposit.getCreateTime());
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("充值失败", e);
            return Result.error("充值失败");
        }
    }

    // 重载方法以支持Controller调用
    public Result<DepositResponse> deposit(Long userId, DepositRequest request) {
        try {
            // 参数验证
            if (!validateDepositRequest(request)) {
                return Result.error("充值参数验证失败");
            }
            
            // 创建充值记录
            Deposit deposit = new Deposit();
            deposit.setUserId(userId);
            deposit.setCurrency(request.getCurrency());
            deposit.setAmount(request.getAmount());
            deposit.setAddress(request.getAddress());
            deposit.setTxHash(request.getTxHash());
            deposit.setConfirmations(0);
            deposit.setRequiredConfirmations(getRequiredConfirmations(request.getCurrency()));
            deposit.setStatus("PENDING"); // 待确认
            deposit.setCreateTime(LocalDateTime.now());
            deposit.setUpdateTime(LocalDateTime.now());
            
            depositMapper.insert(deposit);
            
            // 如果是内部转账或测试环境，直接确认
            if (request.getIsInternal() != null && request.getIsInternal()) {
                confirmDeposit(deposit.getId());
            }
            
            // 构造返回结果
            DepositResponse response = new DepositResponse();
            response.setId(deposit.getId());
            response.setCurrency(deposit.getCurrency());
            response.setAmount(deposit.getAmount());
            response.setStatus(deposit.getStatus());
            response.setCreateTime(deposit.getCreateTime());
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("充值失败", e);
            return Result.error("充值失败");
        }
    }

    @Override
    @Transactional
    public Result<TransactionResponse> withdraw(WithdrawRequest request) {
        try {
            // 参数验证
            if (!validateWithdrawRequest(request)) {
                return Result.error("提现参数验证失败");
            }

            // 验证提现地址
            Result<Boolean> addressValidation = validateWithdrawAddress(request.getCurrency(), request.getAddress());
            if (!addressValidation.isSuccess() || !addressValidation.getData()) {
                return Result.error("提现地址无效");
            }

            // TODO: 需要从请求上下文或参数中获取userId
            log.info("提现请求: {} {}", request.getCurrency(), request.getAmount());
            
            // 创建提现记录
            Withdraw withdraw = new Withdraw();
            // withdraw.setUserId(userId); // 需要从上下文获取
            withdraw.setCurrency(request.getCurrency());
            withdraw.setAmount(request.getAmount());
            withdraw.setFee(calculateWithdrawFee(request.getCurrency(), request.getAmount()));
            withdraw.setToAddress(request.getAddress());
            withdraw.setAddressTag(request.getAddressTag());
            withdraw.setNetwork(request.getNetwork());
            withdraw.setRemark(request.getMemo());
            withdraw.setStatus("PENDING"); // 待处理
            withdraw.setCreateTime(LocalDateTime.now());
            withdraw.setUpdateTime(LocalDateTime.now());

            withdrawMapper.insert(withdraw);

            // TODO: 提交到区块链网络
            processWithdraw(withdraw.getId());

            // 构造返回结果
            TransactionResponse response = new TransactionResponse();
            response.setTransactionId(withdraw.getId());
            response.setCurrency(withdraw.getCurrency());
            response.setAmount(withdraw.getAmount());
            response.setStatus(withdraw.getStatus());
            response.setCreateTime(withdraw.getCreateTime());

            return Result.success(response);
        } catch (Exception e) {
            log.error("提现失败", e);
            return Result.error("提现失败");
        }
    }

    // 重载方法以支持Controller调用
    public Result<WithdrawResponse> withdraw(Long userId, WithdrawRequest request) {
        try {
            // 参数验证
            if (!validateWithdrawRequest(request)) {
                return Result.error("提现参数验证失败");
            }

            // 验证提现地址
            Result<Boolean> addressValidation = validateWithdrawAddress(request.getCurrency(), request.getAddress());
            if (!addressValidation.isSuccess() || !addressValidation.getData()) {
                return Result.error("提现地址无效");
            }

            log.info("提现请求: {} {}", request.getCurrency(), request.getAmount());
            
            // 创建提现记录
            Withdraw withdraw = new Withdraw();
            withdraw.setUserId(userId);
            withdraw.setCurrency(request.getCurrency());
            withdraw.setAmount(request.getAmount());
            withdraw.setFee(calculateWithdrawFee(request.getCurrency(), request.getAmount()));
            withdraw.setToAddress(request.getAddress());
            withdraw.setAddressTag(request.getAddressTag());
            withdraw.setNetwork(request.getNetwork());
            withdraw.setRemark(request.getMemo());
            withdraw.setStatus("PENDING"); // 待处理
            withdraw.setCreateTime(LocalDateTime.now());
            withdraw.setUpdateTime(LocalDateTime.now());

            withdrawMapper.insert(withdraw);

            // TODO: 提交到区块链网络
            processWithdraw(withdraw.getId());

            // 构造返回结果
            WithdrawResponse response = new WithdrawResponse();
            response.setId(withdraw.getId());
            response.setCurrency(withdraw.getCurrency());
            response.setAmount(withdraw.getAmount());
            response.setStatus(withdraw.getStatus());
            response.setApplyTime(withdraw.getCreateTime());

            return Result.success(response);
        } catch (Exception e) {
            log.error("提现失败", e);
            return Result.error("提现失败");
        }
    }

    @Override
    @Transactional
    public Result<Void> transfer(TransferRequest request) {
        try {
            // 参数验证
            if (!validateTransferRequest(request)) {
                return Result.error("转账参数验证失败");
            }

            // 检查源钱包余额
            // TODO: 需要从请求上下文或参数中获取userId
            // 这里暂时使用默认用户ID，实际应该从SecurityContext或JWT中获取
            Long userId = 1L; // 临时解决方案
            log.info("转账请求: {} {} from {} to {}", request.getCurrency(), request.getAmount(), 
                request.getFromAccountType(), request.getToAccountType());
            
            // 记录转账交易
            recordTransfer(userId, request);
            
            return Result.success();
        } catch (Exception e) {
            log.error("转账失败", e);
            return Result.error("转账失败");
        }
    }

    // 重载方法以支持Controller调用
    public Result<TransferResponse> transfer(Long userId, TransferRequest request) {
        try {
            // 参数验证
            if (!validateTransferRequest(request)) {
                return Result.error("转账参数验证失败");
            }

            log.info("转账请求: {} {} from {} to {}", request.getCurrency(), request.getAmount(), 
                request.getFromAccountType(), request.getToAccountType());
            
            // 记录转账交易
            recordTransfer(userId, request);
            
            // 构造返回结果
            TransferResponse response = new TransferResponse();
            response.setCurrency(request.getCurrency());
            response.setAmount(request.getAmount());
            response.setFromAccountType(request.getFromAccountType());
            response.setToAccountType(request.getToAccountType());
            response.setStatus("SUCCESS");
            response.setCreateTime(LocalDateTime.now());
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("转账失败", e);
            return Result.error("转账失败");
        }
    }

    // 移除@Override注解，这个方法签名与接口不匹配
    public Result<PageResult<TransactionResponse>> getTransactionHistory(
            Long userId, String currency, Integer walletType,
            Integer pageNum, Integer pageSize) {
        try {
            // 简化实现：根据用户ID查询交易记录
            List<TransactionRecord> allTransactions = transactionRecordMapper.findByUserId(userId);
            
            // 过滤条件
            List<TransactionRecord> filteredTransactions = allTransactions.stream()
                .filter(t -> currency == null || currency.equals(t.getCurrency()))
                .collect(Collectors.toList());
            
            // 分页处理
            int start = (pageNum - 1) * pageSize;
            int end = Math.min(start + pageSize, filteredTransactions.size());
            List<TransactionRecord> pagedTransactions = filteredTransactions.subList(start, end);
            
            List<TransactionResponse> responses = pagedTransactions.stream()
                .map(this::convertToTransactionResponse)
                .collect(Collectors.toList());

            PageResult<TransactionResponse> pageResult = new PageResult<>();
            pageResult.setRecords(responses);
            pageResult.setTotal((long) filteredTransactions.size());
            pageResult.setCurrent((long) pageNum);
            pageResult.setSize((long) pageSize);

            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("获取交易记录失败", e);
            return Result.error("获取交易记录失败");
        }
    }

    @Override
    public Result<BigDecimal> getUserTotalAssets(Long userId) {
        try {
            List<UserWallet> wallets = userWalletMapper.selectByUserId(userId);
            BigDecimal totalAssets = BigDecimal.ZERO;
            
            for (UserWallet wallet : wallets) {
                BigDecimal walletValue = wallet.getAvailableBalance().add(wallet.getFrozenBalance());
                if (!"USD".equals(wallet.getCurrency())) {
                    // 转换为USD
                    Result<BigDecimal> rateResult = getExchangeRate(wallet.getCurrency(), "USD");
                    if (rateResult.isSuccess()) {
                        walletValue = walletValue.multiply(rateResult.getData());
                    }
                }
                totalAssets = totalAssets.add(walletValue);
            }
            
            return Result.success(totalAssets);
            
        } catch (Exception e) {
            log.error("获取用户总资产失败", e);
            return Result.error("获取总资产失败");
        }
    }

    @Override
    public Result<Object> getUserAssetDistribution(Long userId) {
        try {
            List<UserWallet> wallets = userWalletMapper.selectByUserId(userId);
            
            // 按币种分组统计
            Map<String, BigDecimal> currencyTotals = new HashMap<>();
            for (UserWallet wallet : wallets) {
                 BigDecimal total = wallet.getAvailableBalance().add(wallet.getFrozenBalance());
                currencyTotals.merge(wallet.getCurrency(), total, BigDecimal::add);
            }
            
            // 计算总资产价值
            BigDecimal totalValue = currencyTotals.values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 构建资产项列表
            List<AssetDistributionResponse.AssetItem> assetItems = currencyTotals.entrySet().stream()
                .filter(entry -> entry.getValue().compareTo(BigDecimal.ZERO) > 0)
                .map(entry -> {
                    BigDecimal amount = entry.getValue();
                    BigDecimal percentage = totalValue.compareTo(BigDecimal.ZERO) > 0 ? 
                        amount.divide(totalValue, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")) : BigDecimal.ZERO;
                    
                    return AssetDistributionResponse.AssetItem.builder()
                        .currency(entry.getKey())
                        .totalBalance(amount)
                        .availableBalance(amount)
                        .frozenBalance(BigDecimal.ZERO)
                        .assetValue(amount)
                        .percentage(percentage)
                        .build();
                })
                .collect(Collectors.toList());
            
            // 构建响应对象
            AssetDistributionResponse response = AssetDistributionResponse.builder()
                .userId(userId)
                .totalAssetValue(totalValue)
                .quoteCurrency("USD")
                .assets(assetItems)
                .updateTime(LocalDateTime.now())
                .build();
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("获取资产分布失败", e);
            return Result.error("获取资产分布失败");
        }
    }

    @Override
    public Result<String> generateDepositAddress(Long userId, String currency) {
        try {
            // TODO: 调用区块链服务生成地址
            String address = generateWalletAddress(currency);
            
            // 更新钱包地址
            UserWallet wallet = userWalletMapper.selectByUserAndCurrency(userId, currency, "SPOT");
            if (wallet != null) {
                wallet.setAddress(address);
                wallet.setUpdateTime(LocalDateTime.now());
                userWalletMapper.updateById(wallet);
            }
            
            return Result.success(address);
        } catch (Exception e) {
            log.error("生成充值地址失败", e);
            return Result.error("生成充值地址失败");
        }
    }

    @Override
    public Result<Boolean> validateWithdrawAddress(String currency, String address) {
        try {
            // TODO: 实现地址验证逻辑
            boolean isValid = address != null && address.length() > 10;
            return Result.success(isValid);
        } catch (Exception e) {
            log.error("验证地址失败", e);
            return Result.error("验证地址失败");
        }
    }

    // 移除@Override注解，这个方法不在接口中
    public Result<List<String>> getSupportedCurrencies() {
        try {
            // TODO: 从配置或数据库获取支持的币种列表
            List<String> currencies = Arrays.asList(
                "BTC", "ETH", "USDT", "BNB", "ADA", "XRP", "SOL", "DOT", "DOGE", "AVAX"
            );
            return Result.success(currencies);
        } catch (Exception e) {
            log.error("获取支持币种失败", e);
            return Result.success(Arrays.asList("BTC", "ETH", "USDT"));
        }
    }

    @Override
    public Result<BigDecimal> getExchangeRate(String fromCurrency, String toCurrency) {
        try {
            // TODO: 从外部API获取实时汇率
            BigDecimal rate = fetchExchangeRateFromAPI(fromCurrency, toCurrency);
            return Result.success(rate);
        } catch (Exception e) {
            log.error("获取汇率失败", e);
            return Result.error("获取汇率失败");
        }
    }

    @Override
    @Transactional
    public Result<Object> createAssetSnapshot(Long userId) {
        try {
            // TODO: 实现资产快照功能，需要创建AssetSnapshot实体和AssetSnapshotMapper
            log.info("创建用户{}的资产快照", userId);
            Map<String, Object> snapshot = new HashMap<>();
            snapshot.put("userId", userId);
            snapshot.put("createTime", LocalDateTime.now());
            return Result.success(snapshot);
        } catch (Exception e) {
            log.error("创建资产快照失败", e);
            return Result.error("创建资产快照失败");
        }
    }

    @Override
    @Transactional
    public Result<Void> batchUpdateBalance(List<Object> requests) {
        try {
            // TODO: 实现批量调账功能，需要创建BalanceUpdateRequest类
            log.info("批量调账操作，共{}条记录", requests.size());
            return Result.success();
        } catch (Exception e) {
            log.error("批量调账失败", e);
            return Result.error("批量调账失败");
        }
    }

    // 私有方法
    private WalletResponse convertToWalletResponse(UserWallet wallet) {
        WalletResponse response = new WalletResponse();
        response.setWalletId(wallet.getId());
        response.setUserId(wallet.getUserId());
        response.setCurrency(wallet.getCurrency());
        response.setAccountType(getWalletTypeName(wallet.getWalletType()));
        response.setAvailableBalance(wallet.getAvailableBalance());
        response.setFrozenBalance(wallet.getFrozenBalance());
        response.setTotalBalance(wallet.getAvailableBalance().add(wallet.getFrozenBalance()));
        response.setStatus(wallet.getStatus() == 1 ? "NORMAL" : "DISABLED");
        response.setCreateTime(wallet.getCreateTime());
        response.setUpdateTime(wallet.getUpdateTime());
        return response;
    }

    private TransactionResponse convertToTransactionResponse(TransactionRecord transaction) {
        TransactionResponse response = new TransactionResponse();
        response.setTransactionId(transaction.getId());
        response.setUserId(transaction.getUserId());
        response.setCurrency(transaction.getCurrency());
        response.setAmount(transaction.getAmount());
        response.setType(transaction.getTransactionType());
        response.setAccountType(transaction.getAccountType());
        response.setRelatedOrderId(transaction.getRelatedOrderId());
        response.setMemo(transaction.getRemark());
        response.setCreateTime(transaction.getCreateTime());
        response.setStatus(transaction.getStatus());
        response.setFee(transaction.getFee());
        response.setBalanceBefore(transaction.getBalanceBefore());
        response.setBalanceAfter(transaction.getBalanceAfter());
        response.setTxHash(transaction.getTxHash());
        response.setConfirmations(transaction.getConfirmations());
        response.setAddress(transaction.getAddress());
        response.setNetwork(transaction.getNetwork());
        response.setCompleteTime(transaction.getCompleteTime());
        response.setBusinessType(transaction.getBusinessType());
        return response;
    }

    private void recordTransaction(Long userId, String currency, BigDecimal amount,
                                   Integer walletType, String type, String description) {
        TransactionRecord transaction = new TransactionRecord();
        transaction.setUserId(userId);
        transaction.setCurrency(currency);
        transaction.setAmount(amount);
        transaction.setTransactionType(type);
        transaction.setAccountType(getWalletTypeName(walletType));
        transaction.setRemark(description);
        transaction.setCreateTime(LocalDateTime.now());
        transactionRecordMapper.insert(transaction);
    }

    private void recordTransfer(Long userId, TransferRequest request) {
        // 将账户类型转换为钱包类型
        Integer fromWalletType = getWalletTypeFromAccountType(request.getFromAccountType());
        Integer toWalletType = getWalletTypeFromAccountType(request.getToAccountType());
        
        // 记录转出
        recordTransaction(userId, request.getCurrency(), 
            request.getAmount().negate(), fromWalletType, 
            "TRANSFER_OUT", "转出到" + request.getToAccountType());
        
        // 记录转入
        recordTransaction(userId, request.getCurrency(), 
            request.getAmount(), toWalletType, 
            "TRANSFER_IN", "从" + request.getFromAccountType() + "转入");
    }

    @Override
    public boolean checkBalance(Long userId, String currency, BigDecimal amount, Integer walletType) {
        try {
            UserWallet wallet = userWalletMapper.selectByUserAndCurrency(userId, currency, walletType.toString());
            if (wallet == null) {
                return false;
            }
            
            if (wallet.getAvailableBalance().compareTo(amount) < 0) {
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("检查余额失败", e);
            return false;
        }
    }

    private boolean isSupportedCurrency(String currency) {
        Result<List<String>> result = getSupportedCurrencies();
        return result.isSuccess() && result.getData().contains(currency);
    }

    private String generateWalletAddress(String currency) {
        // TODO: 实现真实的地址生成逻辑
        return currency.toLowerCase() + "_" + System.currentTimeMillis() + "_" + 
               UUID.randomUUID().toString().substring(0, 8);
    }

    private boolean validateDepositRequest(DepositRequest request) {
        return request != null && request.getCurrency() != null && request.getAmount() != null &&
               request.getAmount().compareTo(BigDecimal.ZERO) > 0;
    }

    private boolean validateWithdrawRequest(WithdrawRequest request) {
        return request != null && request.getCurrency() != null && request.getAmount() != null &&
               request.getAmount().compareTo(BigDecimal.ZERO) > 0 &&
               request.getAddress() != null;
    }

    private boolean validateTransferRequest(TransferRequest request) {
        return request != null && request.getCurrency() != null && request.getAmount() != null &&
                request.getAmount().compareTo(BigDecimal.ZERO) > 0 &&
                request.getFromAccountType() != null && request.getToAccountType() != null &&
                !request.getFromAccountType().equals(request.getToAccountType());
    }



    private Integer getRequiredConfirmations(String currency) {
        // TODO: 根据币种返回所需确认数
        switch (currency) {
            case "BTC": return 6;
            case "ETH": return 12;
            case "USDT": return 12;
            default: return 6;
        }
    }

    private BigDecimal calculateWithdrawFee(String currency, BigDecimal amount) {
        // TODO: 实现手续费计算逻辑
        switch (currency) {
            case "BTC": return new BigDecimal("0.0005");
            case "ETH": return new BigDecimal("0.005");
            case "USDT": return new BigDecimal("1.0");
            default: return BigDecimal.ZERO;
        }
    }

    private void confirmDeposit(Long depositId) {
        // TODO: 实现充值确认逻辑
    }

    private void processWithdraw(Long withdrawId) {
        // TODO: 实现提现处理逻辑
    }
    
    private String getWalletTypeName(Integer walletType) {
        switch (walletType) {
            case 1: return "现货钱包";
            case 2: return "合约钱包";
            case 3: return "理财钱包";
            case 4: return "挖矿钱包";
            default: return "未知钱包";
        }
    }
    
    private Integer getWalletTypeFromAccountType(String accountType) {
        switch (accountType) {
            case "SPOT": return 1;
            case "FUTURES": return 2;
            case "MARGIN": return 3;
            default: return 1; // 默认现货钱包
        }
    }
    

    
    private BigDecimal fetchExchangeRateFromAPI(String fromCurrency, String toCurrency) {
        // TODO: 实现从外部API获取汇率的逻辑
        // 这里返回模拟数据
        if ("USDT".equals(fromCurrency) && "USD".equals(toCurrency)) {
            return BigDecimal.ONE;
        }
        if ("BTC".equals(fromCurrency) && "USD".equals(toCurrency)) {
            return new BigDecimal("45000");
        }
        if ("ETH".equals(fromCurrency) && "USD".equals(toCurrency)) {
            return new BigDecimal("3000");
        }
        return BigDecimal.ONE;
    }

    // 删除重复的getUserWallet方法，已在上面定义

    @Override
    public Result<PageResult<TransactionResponse>> getUserTransactions(Long userId, String currency, 
                                                                       Integer type, Integer pageNum, Integer pageSize) {
        try {
            // TODO: 实现分页查询用户交易记录的逻辑
            // 这里返回模拟数据
            PageResult<TransactionResponse> pageResult = PageResult.of((long)pageNum, (long)pageSize, 0L, new ArrayList<>());

            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("获取用户交易记录失败", e);
            return Result.error("获取用户交易记录失败");
        }
    }

    @Override
    public Result<Object> getWalletStatistics(String currency) {
        try {
            // TODO: 实现钱包统计信息的逻辑
            // 这里返回模拟数据
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalUsers", 0);
            statistics.put("totalBalance", BigDecimal.ZERO);
            statistics.put("totalFrozenBalance", BigDecimal.ZERO);
            statistics.put("currency", currency);

            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取钱包统计信息失败", e);
            return Result.error("获取钱包统计信息失败");
        }
    }

    // 添加Controller需要的方法
    public Result<TotalAssetsResponse> getTotalAssets(Long userId) {
        try {
            Result<BigDecimal> totalResult = getUserTotalAssets(userId);
            if (!totalResult.isSuccess()) {
                return Result.error(totalResult.getMessage());
            }
            
            TotalAssetsResponse response = new TotalAssetsResponse();
            response.setUserId(userId);
            response.setTotalAssetValue(totalResult.getData());
            response.setQuoteCurrency("USD");
            response.setUpdateTime(LocalDateTime.now());
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("获取用户总资产失败", e);
            return Result.error("获取总资产失败");
        }
    }

    public Result<List<AssetDistributionResponse>> getAssetDistribution(Long userId) {
        try {
            Result<Object> distributionResult = getUserAssetDistribution(userId);
            if (!distributionResult.isSuccess()) {
                return Result.error(distributionResult.getMessage());
            }
            
            // 简化实现，返回空列表
            List<AssetDistributionResponse> responses = new ArrayList<>();
            return Result.success(responses);
        } catch (Exception e) {
            log.error("获取资产分布失败", e);
            return Result.error("获取资产分布失败");
        }
    }

    public Result<DepositAddressResponse> generateDepositAddress(Long userId, String currency, String network) {
        try {
            Result<String> addressResult = generateDepositAddress(userId, currency);
            if (!addressResult.isSuccess()) {
                return Result.error(addressResult.getMessage());
            }
            
            DepositAddressResponse response = new DepositAddressResponse();
            response.setCurrency(currency);
            response.setNetwork(network);
            response.setAddress(addressResult.getData());
            response.setCreateTime(LocalDateTime.now());
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("生成充值地址失败", e);
            return Result.error("生成充值地址失败");
        }
    }

    public Result<List<DepositAddressResponse>> getDepositAddresses(Long userId, String currency, String network) {
        try {
            // 简化实现，返回空列表
            List<DepositAddressResponse> responses = new ArrayList<>();
            return Result.success(responses);
        } catch (Exception e) {
            log.error("获取充值地址失败", e);
            return Result.error("获取充值地址失败");
        }
    }

    public Result<AddressValidationResponse> validateWithdrawAddress(String currency, String address, String network) {
        try {
            Result<Boolean> validationResult = validateWithdrawAddress(currency, address);
            
            AddressValidationResponse response = new AddressValidationResponse();
            response.setCurrency(currency);
            response.setAddress(address);
            response.setNetwork(network);
            response.setIsValid(validationResult.isSuccess() ? validationResult.getData() : false);
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("验证地址失败", e);
            return Result.error("验证地址失败");
        }
    }

    public Result<List<ExchangeRateResponse>> getExchangeRates(String fromCurrency, String toCurrency) {
        try {
            // 简化实现，返回空列表
            List<ExchangeRateResponse> responses = new ArrayList<>();
            return Result.success(responses);
        } catch (Exception e) {
            log.error("获取汇率失败", e);
            return Result.error("获取汇率失败");
        }
    }

    public Result<AssetSnapshotResponse> createAssetSnapshotForUser(Long userId) {
        try {
            // 直接创建响应，避免递归调用
            AssetSnapshotResponse response = new AssetSnapshotResponse();
            response.setUserId(userId);
            response.setCreateTime(LocalDateTime.now());
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("创建资产快照失败", e);
            return Result.error("创建资产快照失败");
        }
    }

    public Result<PageResult<AssetSnapshotResponse>> getAssetSnapshots(Object request) {
        try {
            // 简化实现，返回空分页结果
            PageResult<AssetSnapshotResponse> pageResult = new PageResult<>();
            pageResult.setRecords(new ArrayList<>());
            pageResult.setTotal(0L);
            pageResult.setCurrent(1L);
            pageResult.setSize(20L);
            
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("获取资产快照失败", e);
            return Result.error("获取资产快照失败");
        }
    }

    public Result<WalletStatisticsResponse> getWalletStatistics(Long userId, String period) {
        try {
            WalletStatisticsResponse response = new WalletStatisticsResponse();
            response.setUserId(userId);
            response.setTimeRange(period);
            response.setUpdateTime(LocalDateTime.now());
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("获取钱包统计失败", e);
            return Result.error("获取钱包统计失败");
        }
    }

    public Result<Void> freezeBalance(Long userId, String currency, BigDecimal amount, String reason) {
        try {
            return freezeBalance(userId, currency, amount);
        } catch (Exception e) {
            log.error("冻结余额失败", e);
            return Result.error("冻结余额失败");
        }
    }

    public Result<Void> unfreezeBalance(Long userId, String currency, BigDecimal amount, String reason) {
        try {
            return unfreezeBalance(userId, currency, amount);
        } catch (Exception e) {
            log.error("解冻余额失败", e);
            return Result.error("解冻余额失败");
        }
    }

    public Result<PageResult<BalanceChangeResponse>> getBalanceChanges(Object request) {
        try {
            // 简化实现，返回空分页结果
            PageResult<BalanceChangeResponse> pageResult = new PageResult<>();
            pageResult.setRecords(new ArrayList<>());
            pageResult.setTotal(0L);
            pageResult.setCurrent(1L);
            pageResult.setSize(20L);
            
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("获取余额变动记录失败", e);
            return Result.error("获取余额变动记录失败");
        }
    }

    public Result<List<CurrencyResponse>> getSupportedCurrenciesWithDetails() {
        try {
            // 简化实现，返回常用币种
            List<String> currencies = Arrays.asList("BTC", "ETH", "USDT", "BNB", "ADA");
            
            List<CurrencyResponse> responses = currencies.stream()
                .map(currency -> {
                    CurrencyResponse response = new CurrencyResponse();
                    response.setCurrency(currency);
                    response.setName(currency);
                    response.setTradingEnabled(true);
                    return response;
                })
                .collect(Collectors.toList());
            
            return Result.success(responses);
        } catch (Exception e) {
            log.error("获取支持币种失败", e);
            return Result.error("获取支持币种失败");
        }
    }

    public Result<List<NetworkConfigResponse>> getNetworkConfigs(String currency) {
        try {
            // 简化实现，返回空列表
            List<NetworkConfigResponse> responses = new ArrayList<>();
            return Result.success(responses);
        } catch (Exception e) {
            log.error("获取网络配置失败", e);
            return Result.error("获取网络配置失败");
        }
    }

    public Result<WithdrawLimitResponse> getWithdrawLimits(Long userId, String currency) {
        try {
            WithdrawLimitResponse response = new WithdrawLimitResponse();
            response.setUserId(userId);
            response.setCurrency(currency);
            response.setDailyLimit(new BigDecimal("10000"));
            response.setTodayRemaining(new BigDecimal("10000"));
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("获取提现限额失败", e);
            return Result.error("获取提现限额失败");
        }
    }

    public Result<List<FeeInfoResponse>> getFeeInfo(String currency, String type) {
        try {
            // 简化实现，返回空列表
            List<FeeInfoResponse> responses = new ArrayList<>();
            return Result.success(responses);
        } catch (Exception e) {
            log.error("获取手续费信息失败", e);
            return Result.error("获取手续费信息失败");
        }
    }

    public Result<PageResult<DepositRecordResponse>> getDepositRecords(Object request) {
        try {
            // 简化实现，返回空分页结果
            PageResult<DepositRecordResponse> pageResult = new PageResult<>();
            pageResult.setRecords(new ArrayList<>());
            pageResult.setTotal(0L);
            pageResult.setCurrent(1L);
            pageResult.setSize(20L);
            
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("获取充值记录失败", e);
            return Result.error("获取充值记录失败");
        }
    }

    public Result<PageResult<WithdrawRecordResponse>> getWithdrawRecords(Object request) {
        try {
            // 简化实现，返回空分页结果
            PageResult<WithdrawRecordResponse> pageResult = new PageResult<>();
            pageResult.setRecords(new ArrayList<>());
            pageResult.setTotal(0L);
            pageResult.setCurrent(1L);
            pageResult.setSize(20L);
            
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("获取提现记录失败", e);
            return Result.error("获取提现记录失败");
        }
    }

    public Result<PageResult<TransferRecordResponse>> getTransferRecords(Object request) {
        try {
            // 简化实现，返回空分页结果
            PageResult<TransferRecordResponse> pageResult = new PageResult<>();
            pageResult.setRecords(new ArrayList<>());
            pageResult.setTotal(0L);
            pageResult.setCurrent(1L);
            pageResult.setSize(20L);
            
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("获取转账记录失败", e);
            return Result.error("获取转账记录失败");
        }
    }
}
